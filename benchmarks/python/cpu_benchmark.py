import sys
import time
import random
import math

class CPUBenchmark:
    @staticmethod
    def prime_generation(limit):
        """Sieve of Eratosthenes for prime generation"""
        is_prime = [True] * (limit + 1)
        is_prime[0] = is_prime[1] = False
        
        for i in range(2, int(math.sqrt(limit)) + 1):
            if is_prime[i]:
                for j in range(i * i, limit + 1, i):
                    is_prime[j] = False
        
        count = sum(is_prime)
        print(f"Found {count} primes up to {limit}")
    
    @staticmethod
    def fibonacci(n):
        """<PERSON><PERSON><PERSON><PERSON> calculation (iterative for performance)"""
        if n <= 0:
            return
        
        a, b = 0, 1
        for i in range(2, n + 1):
            a, b = b, a + b
        
        print(f"<PERSON><PERSON><PERSON><PERSON>({n}) = {b}")
    
    @staticmethod
    def matrix_multiplication(size):
        """Matrix multiplication benchmark"""
        # Initialize matrices with random values
        random.seed(42)  # Fixed seed for consistency
        a = [[random.random() for _ in range(size)] for _ in range(size)]
        b = [[random.random() for _ in range(size)] for _ in range(size)]
        c = [[0.0 for _ in range(size)] for _ in range(size)]
        
        # Multiply matrices
        for i in range(size):
            for j in range(size):
                for k in range(size):
                    c[i][j] += a[i][k] * b[k][j]
        
        print(f"Matrix multiplication completed for {size}x{size} matrices")
    
    @staticmethod
    def sorting_benchmark(size):
        """QuickSort benchmark"""
        random.seed(42)
        array = [random.randint(0, size - 1) for _ in range(size)]
        
        CPUBenchmark._quick_sort(array, 0, len(array) - 1)
        
        print(f"Sorted array of {size} elements")
    
    @staticmethod
    def pi_calculation(iterations):
        """Pi calculation using Monte Carlo method"""
        random.seed(42)
        inside_circle = 0
        
        for _ in range(iterations):
            x = random.random()
            y = random.random()
            
            if x * x + y * y <= 1.0:
                inside_circle += 1
        
        pi = 4.0 * inside_circle / iterations
        print(f"Calculated Pi = {pi} (iterations: {iterations})")
    
    @staticmethod
    def _quick_sort(arr, low, high):
        """QuickSort implementation"""
        if low < high:
            pi = CPUBenchmark._partition(arr, low, high)
            CPUBenchmark._quick_sort(arr, low, pi - 1)
            CPUBenchmark._quick_sort(arr, pi + 1, high)
    
    @staticmethod
    def _partition(arr, low, high):
        """Partition function for QuickSort"""
        pivot = arr[high]
        i = low - 1
        
        for j in range(low, high):
            if arr[j] < pivot:
                i += 1
                arr[i], arr[j] = arr[j], arr[i]
        
        arr[i + 1], arr[high] = arr[high], arr[i + 1]
        return i + 1

def main():
    if len(sys.argv) < 3:
        print("Usage: python3 cpu_benchmark.py <test_type> <parameter>", file=sys.stderr)
        sys.exit(1)
    
    test_type = sys.argv[1]
    parameter = int(sys.argv[2])
    
    start_time = time.time()
    
    if test_type == "prime":
        CPUBenchmark.prime_generation(parameter)
    elif test_type == "fibonacci":
        CPUBenchmark.fibonacci(parameter)
    elif test_type == "matrix":
        CPUBenchmark.matrix_multiplication(parameter)
    elif test_type == "sort":
        CPUBenchmark.sorting_benchmark(parameter)
    elif test_type == "pi":
        CPUBenchmark.pi_calculation(parameter)
    else:
        print(f"Unknown test type: {test_type}", file=sys.stderr)
        sys.exit(1)
    
    end_time = time.time()
    duration = (end_time - start_time) * 1000  # Convert to milliseconds
    
    print(f"Execution time: {int(duration)} ms")

if __name__ == "__main__":
    main()
