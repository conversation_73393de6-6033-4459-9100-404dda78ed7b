class CPUBenchmark {
    static primeGeneration(limit) {
        const isPrime = new Array(limit + 1).fill(true);
        isPrime[0] = isPrime[1] = false;
        
        for (let i = 2; i * i <= limit; i++) {
            if (isPrime[i]) {
                for (let j = i * i; j <= limit; j += i) {
                    isPrime[j] = false;
                }
            }
        }
        
        let count = 0;
        for (const prime of isPrime) {
            if (prime) count++;
        }
        
        console.log(`Found ${count} primes up to ${limit}`);
    }
    
    static fibonacci(n) {
        if (n <= 0) return;
        
        let a = 0n, b = 1n; // Using BigInt for large numbers
        for (let i = 2; i <= n; i++) {
            const temp = a + b;
            a = b;
            b = temp;
        }
        
        console.log(`Fibonacci(${n}) = ${b}`);
    }
    
    static matrixMultiplication(size) {
        const a = Array(size).fill().map(() => Array(size));
        const b = Array(size).fill().map(() => Array(size));
        const c = Array(size).fill().map(() => Array(size).fill(0));
        
        // Initialize matrices with random values (fixed seed simulation)
        let seed = 42;
        const random = () => {
            seed = (seed * 9301 + 49297) % 233280;
            return seed / 233280;
        };
        
        for (let i = 0; i < size; i++) {
            for (let j = 0; j < size; j++) {
                a[i][j] = random();
                b[i][j] = random();
            }
        }
        
        // Multiply matrices
        for (let i = 0; i < size; i++) {
            for (let j = 0; j < size; j++) {
                for (let k = 0; k < size; k++) {
                    c[i][j] += a[i][k] * b[k][j];
                }
            }
        }
        
        console.log(`Matrix multiplication completed for ${size}x${size} matrices`);
    }
    
    static sortingBenchmark(size) {
        const array = new Array(size);
        
        // Initialize array with random values (fixed seed simulation)
        let seed = 42;
        const random = () => {
            seed = (seed * 9301 + 49297) % 233280;
            return Math.floor((seed / 233280) * size);
        };
        
        for (let i = 0; i < size; i++) {
            array[i] = random();
        }
        
        this.quickSort(array, 0, array.length - 1);
        
        console.log(`Sorted array of ${size} elements`);
    }
    
    static piCalculation(iterations) {
        let seed = 42;
        const random = () => {
            seed = (seed * 9301 + 49297) % 233280;
            return seed / 233280;
        };
        
        let insideCircle = 0;
        
        for (let i = 0; i < iterations; i++) {
            const x = random();
            const y = random();
            
            if (x * x + y * y <= 1.0) {
                insideCircle++;
            }
        }
        
        const pi = 4.0 * insideCircle / iterations;
        console.log(`Calculated Pi = ${pi} (iterations: ${iterations})`);
    }
    
    static quickSort(arr, low, high) {
        if (low < high) {
            const pi = this.partition(arr, low, high);
            this.quickSort(arr, low, pi - 1);
            this.quickSort(arr, pi + 1, high);
        }
    }
    
    static partition(arr, low, high) {
        const pivot = arr[high];
        let i = low - 1;
        
        for (let j = low; j < high; j++) {
            if (arr[j] < pivot) {
                i++;
                [arr[i], arr[j]] = [arr[j], arr[i]]; // Swap
            }
        }
        
        [arr[i + 1], arr[high]] = [arr[high], arr[i + 1]]; // Swap
        return i + 1;
    }
}

// Main execution
function main() {
    const args = process.argv.slice(2);
    
    if (args.length < 2) {
        console.error('Usage: node cpu_benchmark.js <test_type> <parameter>');
        process.exit(1);
    }
    
    const testType = args[0];
    const parameter = parseInt(args[1]);
    
    const startTime = process.hrtime.bigint();
    
    switch (testType) {
        case 'prime':
            CPUBenchmark.primeGeneration(parameter);
            break;
        case 'fibonacci':
            CPUBenchmark.fibonacci(parameter);
            break;
        case 'matrix':
            CPUBenchmark.matrixMultiplication(parameter);
            break;
        case 'sort':
            CPUBenchmark.sortingBenchmark(parameter);
            break;
        case 'pi':
            CPUBenchmark.piCalculation(parameter);
            break;
        default:
            console.error(`Unknown test type: ${testType}`);
            process.exit(1);
    }
    
    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - startTime) / 1_000_000; // Convert to milliseconds
    
    console.log(`Execution time: ${Math.round(duration)} ms`);
}

if (require.main === module) {
    main();
}
