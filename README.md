# Multi-Language Performance Benchmark Suite

Một hệ thống benchmark toàn diện để so sánh hiệu năng giữa **Java, C++, JavaScript (Node.js), Rust, và Python3** trong các tình huống khác nhau.

## 🎯 Mục tiêu

Chứng minh các điểm sau về hiệu năng ngôn ngữ lập trình:

1. **C++ thống trị Cold Start**: C++ nhanh hơn tất cả ngôn ngữ khác khi chưa có warm-up
2. **Java Comeback Effect**: Java có thể vượt qua C++ sau khi JIT compiler warm-up
3. **Memory Usage Analysis**: So sánh tiêu thụ bộ nhớ giữa các ngôn ngữ
4. **Multi-threading Performance**: Đánh giá hiệu năng đa luồng

## 🏗️ Kiến trúc

```
benmark/
├── run_benchmark.sh          # Script chính để chạy tất cả benchmarks
├── setup.sh                  # Script cài đặt dependencies
├── benchmarks/               # Source code cho từng ngôn ngữ
│   ├── java/                # Java implementations
│   ├── cpp/                 # C++ implementations  
│   ├── javascript/          # Node.js implementations
│   ├── rust/                # Rust implementations
│   └── python/              # Python implementations
├── results/                 # Kết quả benchmark và charts
├── analysis/                # Python scripts phân tích dữ liệu
└── requirements.txt         # Python dependencies
```

## 🧪 Test Cases

### CPU-Bound Tests
- **Prime Generation**: Sieve of Eratosthenes (1M numbers)
- **Fibonacci**: Iterative calculation (n=45)
- **Matrix Multiplication**: 500x500 matrices
- **QuickSort**: Sorting 1M random integers
- **Pi Calculation**: Monte Carlo method (10M iterations)

### Memory-Bound Tests (Coming Soon)
- Large array operations
- Memory allocation patterns
- Cache performance tests

### I/O-Bound Tests (Coming Soon)
- File operations
- JSON parsing/serialization
- String processing

### Multi-Threading Tests (Coming Soon)
- Producer-Consumer patterns
- Parallel sorting
- Thread pool performance

## 🚀 Quick Start

### 1. Cài đặt Dependencies

```bash
# Tự động cài đặt (Ubuntu/Debian/macOS/Fedora)
./setup.sh

# Hoặc cài đặt thủ công:
# - Java 17+ (OpenJDK)
# - C++ compiler (g++)
# - Node.js 18+
# - Python 3.9+
# - Rust 1.70+
# - bc calculator
```

### 2. Chạy Benchmarks

```bash
# Chạy tất cả benchmarks và tạo báo cáo
./run_benchmark.sh

# Kết quả sẽ được lưu trong results/benchmark_results.json
# Charts sẽ được tạo trong results/charts/
```

### 3. Phân tích Kết quả

```bash
# Chỉ phân tích (không tạo charts)
python3 analysis/analyze_results.py

# Phân tích và tạo charts
python3 analysis/analyze_results.py --charts

# Chỉ tạo charts
python3 analysis/generate_charts.py
```

## 📊 Output và Báo cáo

### JSON Output Format
```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "language": "Java",
  "test_category": "CPU",
  "test_name": "PrimeGeneration", 
  "execution_time_ms": 1250.5,
  "memory_usage_mb": 45.2,
  "iteration": 1,
  "thread_count": 1,
  "is_warmup": false
}
```

### Generated Charts
- **Cold Start Comparison**: Hiệu năng lần chạy đầu tiên
- **Warmup Curves**: Đường cong cải thiện hiệu năng qua các iteration
- **Performance Heatmap**: Ma trận so sánh tương đối
- **Language Rankings**: Xếp hạng tổng thể
- **Memory Usage**: So sánh tiêu thụ bộ nhớ
- **Cold vs Warm**: So sánh trực tiếp cold start vs warmed up

### Console Report
```
🏆 PERFORMANCE CHAMPIONS
Cold Start Winners:
  PrimeGeneration : C++
  Fibonacci       : Rust
  MatrixMult      : C++

Warmed Up Winners:
  PrimeGeneration : Java
  Fibonacci       : Java  
  MatrixMult      : C++
```

## 🔬 Methodology

### Warm-up Process
- **5 warmup iterations**: Để JIT compiler optimize
- **10 benchmark iterations**: Đo hiệu năng thực tế
- **Fixed random seeds**: Đảm bảo consistency

### Measurement
- **Execution Time**: Nano-second precision
- **Memory Usage**: System memory monitoring
- **Multiple Runs**: Statistical significance

### Optimization Levels
- **Java**: JIT optimization tự động
- **C++**: -O3 optimization flag
- **Rust**: --release build với LTO
- **JavaScript**: V8 engine optimization
- **Python**: Standard CPython interpreter

## 📈 Expected Results

### Cold Start (First Run)
```
1. C++        : ~100ms (compiled, optimized)
2. Rust       : ~120ms (compiled, zero-cost abstractions)  
3. JavaScript : ~200ms (V8 JIT, fast startup)
4. Java       : ~500ms (JVM startup overhead)
5. Python     : ~800ms (interpreted, dynamic typing)
```

### Warmed Up (After JIT)
```
1. Java       : ~80ms  (HotSpot JIT optimization)
2. C++        : ~100ms (static optimization)
3. Rust       : ~110ms (LLVM optimization)
4. JavaScript : ~150ms (V8 optimization)
5. Python     : ~750ms (still interpreted)
```

## 🎨 Customization

### Thêm Test Cases Mới
1. Tạo implementation trong từng thư mục ngôn ngữ
2. Cập nhật `run_benchmark.sh` để include test mới
3. Chạy lại benchmark

### Thay đổi Parameters
Sửa các giá trị trong `run_benchmark.sh`:
- `WARMUP_ITERATIONS`: Số lần warm-up
- `BENCHMARK_ITERATIONS`: Số lần đo benchmark
- Test parameters (array sizes, iteration counts, etc.)

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch
3. Implement new test cases hoặc improvements
4. Submit pull request

## 📝 License

MIT License - Xem file LICENSE để biết chi tiết.

## 🔗 References

- [Java JMH Benchmarking](https://openjdk.java.net/projects/code-tools/jmh/)
- [C++ Optimization Guide](https://gcc.gnu.org/onlinedocs/gcc/Optimize-Options.html)
- [Rust Performance Book](https://nnethercote.github.io/perf-book/)
- [V8 JavaScript Engine](https://v8.dev/)
- [Python Performance Tips](https://wiki.python.org/moin/PythonSpeed)
