#!/usr/bin/env python3
"""
Multi-Language Benchmark Results Analyzer
Analyzes performance data from Java, C++, JavaScript, Rust, and Python benchmarks
"""

import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import argparse
from datetime import datetime

class BenchmarkAnalyzer:
    def __init__(self, results_file="results/benchmark_results.json"):
        self.results_file = results_file
        self.df = None
        self.load_data()
        
    def load_data(self):
        """Load benchmark results from JSON file"""
        try:
            with open(self.results_file, 'r') as f:
                data = json.load(f)
            
            if not data:
                print("No benchmark data found!")
                return
                
            self.df = pd.DataFrame(data)
            print(f"Loaded {len(self.df)} benchmark results")
            
            # Convert timestamp to datetime
            self.df['timestamp'] = pd.to_datetime(self.df['timestamp'])
            
            # Separate warmup and actual benchmark data
            self.warmup_df = self.df[self.df['is_warmup'] == True]
            self.benchmark_df = self.df[self.df['is_warmup'] == False]
            
            print(f"Warmup results: {len(self.warmup_df)}")
            print(f"Benchmark results: {len(self.benchmark_df)}")
            
        except FileNotFoundError:
            print(f"Results file {self.results_file} not found!")
        except json.JSONDecodeError:
            print(f"Invalid JSON in {self.results_file}")
    
    def generate_summary_stats(self):
        """Generate summary statistics for all languages and tests"""
        if self.benchmark_df is None or self.benchmark_df.empty:
            print("No benchmark data available for analysis")
            return
        
        print("\n" + "="*80)
        print("BENCHMARK SUMMARY STATISTICS")
        print("="*80)
        
        # Group by language and test
        summary = self.benchmark_df.groupby(['language', 'test_category', 'test_name']).agg({
            'execution_time_ms': ['mean', 'std', 'min', 'max'],
            'memory_usage_mb': ['mean', 'std', 'min', 'max']
        }).round(2)
        
        print(summary)
        
        # Language performance ranking
        print("\n" + "-"*60)
        print("AVERAGE PERFORMANCE BY LANGUAGE (CPU Tests)")
        print("-"*60)
        
        cpu_tests = self.benchmark_df[self.benchmark_df['test_category'] == 'CPU']
        if not cpu_tests.empty:
            lang_performance = cpu_tests.groupby('language')['execution_time_ms'].mean().sort_values()
            
            for i, (lang, time) in enumerate(lang_performance.items(), 1):
                print(f"{i}. {lang:<12}: {time:>8.2f} ms (avg)")
        
        return summary
    
    def analyze_warmup_effect(self):
        """Analyze the warmup effect, especially for Java"""
        if self.df is None or self.df.empty:
            return
        
        print("\n" + "="*80)
        print("WARMUP ANALYSIS")
        print("="*80)
        
        # Compare first iteration vs last iteration performance
        for lang in self.df['language'].unique():
            lang_data = self.df[self.df['language'] == lang]
            
            if lang_data.empty:
                continue
                
            print(f"\n{lang} Performance Evolution:")
            print("-" * 40)
            
            for test_name in lang_data['test_name'].unique():
                test_data = lang_data[lang_data['test_name'] == test_name].sort_values('iteration')
                
                if len(test_data) < 2:
                    continue
                
                first_iter = test_data.iloc[0]['execution_time_ms']
                last_iter = test_data.iloc[-1]['execution_time_ms']
                improvement = ((first_iter - last_iter) / first_iter) * 100
                
                print(f"  {test_name:<15}: {first_iter:>8.2f}ms → {last_iter:>8.2f}ms ({improvement:>+6.1f}%)")
    
    def compare_cold_vs_warm(self):
        """Compare cold start vs warmed up performance"""
        if self.df is None or self.df.empty:
            return
        
        print("\n" + "="*80)
        print("COLD START vs WARMED UP COMPARISON")
        print("="*80)
        
        # Get first warmup iteration (cold start) vs last benchmark iteration (warmed up)
        results = []
        
        for lang in self.df['language'].unique():
            for test_name in self.df['test_name'].unique():
                # Cold start (first warmup iteration)
                cold_data = self.df[
                    (self.df['language'] == lang) & 
                    (self.df['test_name'] == test_name) & 
                    (self.df['is_warmup'] == True) & 
                    (self.df['iteration'] == 1)
                ]
                
                # Warmed up (last benchmark iteration)
                warm_data = self.df[
                    (self.df['language'] == lang) & 
                    (self.df['test_name'] == test_name) & 
                    (self.df['is_warmup'] == False)
                ]
                
                if not cold_data.empty and not warm_data.empty:
                    cold_time = cold_data.iloc[0]['execution_time_ms']
                    warm_time = warm_data['execution_time_ms'].mean()
                    
                    results.append({
                        'language': lang,
                        'test_name': test_name,
                        'cold_start_ms': cold_time,
                        'warmed_up_ms': warm_time,
                        'improvement_percent': ((cold_time - warm_time) / cold_time) * 100
                    })
        
        if results:
            comparison_df = pd.DataFrame(results)
            
            # Show results by test
            for test_name in comparison_df['test_name'].unique():
                test_results = comparison_df[comparison_df['test_name'] == test_name].sort_values('cold_start_ms')
                
                print(f"\n{test_name} - Cold Start Performance:")
                print("-" * 50)
                for _, row in test_results.iterrows():
                    print(f"  {row['language']:<12}: {row['cold_start_ms']:>8.2f}ms")
                
                print(f"\n{test_name} - Warmed Up Performance:")
                print("-" * 50)
                test_results_warm = test_results.sort_values('warmed_up_ms')
                for _, row in test_results_warm.iterrows():
                    improvement = row['improvement_percent']
                    print(f"  {row['language']:<12}: {row['warmed_up_ms']:>8.2f}ms ({improvement:>+6.1f}%)")
    
    def generate_performance_report(self):
        """Generate comprehensive performance report"""
        if self.df is None or self.df.empty:
            print("No data available for report generation")
            return
        
        print("\n" + "="*80)
        print("COMPREHENSIVE PERFORMANCE REPORT")
        print("="*80)
        
        # Overall winner analysis
        print("\n🏆 PERFORMANCE CHAMPIONS")
        print("-" * 40)
        
        # Cold start champions
        cold_winners = {}
        warm_winners = {}
        
        for test_name in self.df['test_name'].unique():
            # Cold start winner (first iteration)
            cold_data = self.df[
                (self.df['test_name'] == test_name) & 
                (self.df['iteration'] == 1) & 
                (self.df['is_warmup'] == True)
            ]
            
            if not cold_data.empty:
                cold_winner = cold_data.loc[cold_data['execution_time_ms'].idxmin()]
                cold_winners[test_name] = cold_winner['language']
            
            # Warmed up winner (average of benchmark iterations)
            warm_data = self.benchmark_df[self.benchmark_df['test_name'] == test_name]
            if not warm_data.empty:
                warm_avg = warm_data.groupby('language')['execution_time_ms'].mean()
                warm_winners[test_name] = warm_avg.idxmin()
        
        print("Cold Start Winners:")
        for test, winner in cold_winners.items():
            print(f"  {test:<15}: {winner}")
        
        print("\nWarmed Up Winners:")
        for test, winner in warm_winners.items():
            print(f"  {test:<15}: {winner}")
        
        # Language win counts
        cold_wins = pd.Series(list(cold_winners.values())).value_counts()
        warm_wins = pd.Series(list(warm_winners.values())).value_counts()
        
        print(f"\n📊 WIN COUNTS")
        print("-" * 30)
        print("Cold Start Wins:")
        for lang, wins in cold_wins.items():
            print(f"  {lang:<12}: {wins}")
        
        print("\nWarmed Up Wins:")
        for lang, wins in warm_wins.items():
            print(f"  {lang:<12}: {wins}")

def main():
    parser = argparse.ArgumentParser(description='Analyze multi-language benchmark results')
    parser.add_argument('--results', '-r', default='results/benchmark_results.json',
                       help='Path to benchmark results JSON file')
    parser.add_argument('--charts', '-c', action='store_true',
                       help='Generate charts (requires matplotlib)')
    
    args = parser.parse_args()
    
    analyzer = BenchmarkAnalyzer(args.results)
    
    if analyzer.df is not None and not analyzer.df.empty:
        analyzer.generate_summary_stats()
        analyzer.analyze_warmup_effect()
        analyzer.compare_cold_vs_warm()
        analyzer.generate_performance_report()
        
        if args.charts:
            print("\n" + "="*80)
            print("GENERATING CHARTS...")
            print("="*80)
            from generate_charts import ChartGenerator
            chart_gen = ChartGenerator(analyzer.df)
            chart_gen.generate_all_charts()
    else:
        print("No data available for analysis. Run benchmarks first!")

if __name__ == "__main__":
    main()
