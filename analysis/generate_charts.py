#!/usr/bin/env python3
"""
Chart Generator for Multi-Language Benchmark Results
Creates comprehensive visualizations comparing Java, C++, JavaScript, Rust, and Python
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from pathlib import Path
import json

class ChartGenerator:
    def __init__(self, df):
        self.df = df
        self.output_dir = Path("results/charts")
        self.output_dir.mkdir(exist_ok=True)
        
        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # Language colors for consistency
        self.language_colors = {
            'Java': '#f89820',
            'C++': '#00599c',
            'JavaScript': '#f7df1e',
            'Rust': '#ce422b',
            'Python': '#3776ab'
        }
    
    def generate_all_charts(self):
        """Generate all benchmark visualization charts"""
        print("Generating performance comparison charts...")
        
        if self.df is None or self.df.empty:
            print("No data available for chart generation")
            return
        
        # Generate different types of charts
        self.plot_cold_start_comparison()
        self.plot_warmup_curves()
        self.plot_performance_heatmap()
        self.plot_language_rankings()
        self.plot_memory_usage_comparison()
        self.plot_performance_distribution()
        self.plot_cold_vs_warm_comparison()
        
        print(f"Charts saved to: {self.output_dir}")
    
    def plot_cold_start_comparison(self):
        """Plot cold start performance comparison"""
        # Get first iteration (cold start) data
        cold_data = self.df[
            (self.df['iteration'] == 1) & 
            (self.df['is_warmup'] == True)
        ]
        
        if cold_data.empty:
            return
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Cold Start Performance Comparison\n(First Execution - No JIT Optimization)', 
                    fontsize=16, fontweight='bold')
        
        test_names = cold_data['test_name'].unique()
        
        for i, test_name in enumerate(test_names[:6]):  # Limit to 6 tests
            row, col = i // 3, i % 3
            ax = axes[row, col]
            
            test_data = cold_data[cold_data['test_name'] == test_name]
            test_data = test_data.sort_values('execution_time_ms')
            
            bars = ax.bar(test_data['language'], test_data['execution_time_ms'],
                         color=[self.language_colors.get(lang, 'gray') for lang in test_data['language']])
            
            ax.set_title(f'{test_name}', fontweight='bold')
            ax.set_ylabel('Execution Time (ms)')
            ax.tick_params(axis='x', rotation=45)
            
            # Add value labels on bars
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{height:.1f}ms', ha='center', va='bottom', fontsize=9)
        
        # Remove empty subplots
        for i in range(len(test_names), 6):
            row, col = i // 3, i % 3
            fig.delaxes(axes[row, col])
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'cold_start_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_warmup_curves(self):
        """Plot warmup curves showing performance improvement over iterations"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Performance Warmup Curves\n(Showing JIT Optimization Effect)', 
                    fontsize=16, fontweight='bold')
        
        test_names = self.df['test_name'].unique()
        
        for i, test_name in enumerate(test_names[:6]):
            row, col = i // 3, i % 3
            ax = axes[row, col]
            
            for lang in self.df['language'].unique():
                lang_test_data = self.df[
                    (self.df['language'] == lang) & 
                    (self.df['test_name'] == test_name)
                ].sort_values('iteration')
                
                if not lang_test_data.empty:
                    ax.plot(lang_test_data['iteration'], lang_test_data['execution_time_ms'],
                           marker='o', label=lang, color=self.language_colors.get(lang, 'gray'),
                           linewidth=2, markersize=4)
            
            ax.set_title(f'{test_name}', fontweight='bold')
            ax.set_xlabel('Iteration')
            ax.set_ylabel('Execution Time (ms)')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
        
        # Remove empty subplots
        for i in range(len(test_names), 6):
            row, col = i // 3, i % 3
            fig.delaxes(axes[row, col])
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'warmup_curves.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_performance_heatmap(self):
        """Create performance heatmap across languages and tests"""
        # Use benchmark data (warmed up)
        benchmark_data = self.df[self.df['is_warmup'] == False]
        
        if benchmark_data.empty:
            return
        
        # Calculate average performance for each language-test combination
        heatmap_data = benchmark_data.groupby(['language', 'test_name'])['execution_time_ms'].mean().unstack()
        
        # Normalize by test (to show relative performance)
        heatmap_normalized = heatmap_data.div(heatmap_data.min(axis=0), axis=1)
        
        plt.figure(figsize=(12, 8))
        sns.heatmap(heatmap_normalized, annot=True, fmt='.2f', cmap='RdYlGn_r',
                   cbar_kws={'label': 'Relative Performance (1.0 = Best)'})
        
        plt.title('Performance Heatmap (Warmed Up)\nLower values = Better performance', 
                 fontsize=14, fontweight='bold')
        plt.xlabel('Test Cases')
        plt.ylabel('Programming Languages')
        plt.tight_layout()
        plt.savefig(self.output_dir / 'performance_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_language_rankings(self):
        """Plot overall language rankings"""
        # Calculate average performance across all tests
        cold_avg = self.df[
            (self.df['iteration'] == 1) & 
            (self.df['is_warmup'] == True)
        ].groupby('language')['execution_time_ms'].mean().sort_values()
        
        warm_avg = self.df[
            self.df['is_warmup'] == False
        ].groupby('language')['execution_time_ms'].mean().sort_values()
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Cold start rankings
        bars1 = ax1.bar(range(len(cold_avg)), cold_avg.values,
                       color=[self.language_colors.get(lang, 'gray') for lang in cold_avg.index])
        ax1.set_title('Cold Start Performance\n(Average across all tests)', fontweight='bold')
        ax1.set_ylabel('Average Execution Time (ms)')
        ax1.set_xticks(range(len(cold_avg)))
        ax1.set_xticklabels(cold_avg.index, rotation=45)
        
        # Add value labels
        for i, bar in enumerate(bars1):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.1f}ms', ha='center', va='bottom')
        
        # Warmed up rankings
        bars2 = ax2.bar(range(len(warm_avg)), warm_avg.values,
                       color=[self.language_colors.get(lang, 'gray') for lang in warm_avg.index])
        ax2.set_title('Warmed Up Performance\n(Average across all tests)', fontweight='bold')
        ax2.set_ylabel('Average Execution Time (ms)')
        ax2.set_xticks(range(len(warm_avg)))
        ax2.set_xticklabels(warm_avg.index, rotation=45)
        
        # Add value labels
        for i, bar in enumerate(bars2):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.1f}ms', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'language_rankings.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_memory_usage_comparison(self):
        """Plot memory usage comparison"""
        if 'memory_usage_mb' not in self.df.columns:
            return
        
        # Average memory usage by language
        memory_data = self.df.groupby('language')['memory_usage_mb'].mean().sort_values()
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(memory_data.index, memory_data.values,
                      color=[self.language_colors.get(lang, 'gray') for lang in memory_data.index])
        
        plt.title('Average Memory Usage by Language', fontsize=14, fontweight='bold')
        plt.ylabel('Memory Usage (MB)')
        plt.xticks(rotation=45)
        
        # Add value labels
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.1f}MB', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'memory_usage_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_performance_distribution(self):
        """Plot performance distribution using box plots"""
        benchmark_data = self.df[self.df['is_warmup'] == False]
        
        if benchmark_data.empty:
            return
        
        plt.figure(figsize=(12, 8))
        sns.boxplot(data=benchmark_data, x='language', y='execution_time_ms', hue='test_name')
        
        plt.title('Performance Distribution (Warmed Up)\nBox plots showing quartiles and outliers', 
                 fontsize=14, fontweight='bold')
        plt.xlabel('Programming Language')
        plt.ylabel('Execution Time (ms)')
        plt.xticks(rotation=45)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'performance_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_cold_vs_warm_comparison(self):
        """Plot direct comparison of cold vs warm performance"""
        # Calculate cold start and warm averages
        results = []
        
        for lang in self.df['language'].unique():
            for test_name in self.df['test_name'].unique():
                cold_data = self.df[
                    (self.df['language'] == lang) & 
                    (self.df['test_name'] == test_name) & 
                    (self.df['is_warmup'] == True) & 
                    (self.df['iteration'] == 1)
                ]
                
                warm_data = self.df[
                    (self.df['language'] == lang) & 
                    (self.df['test_name'] == test_name) & 
                    (self.df['is_warmup'] == False)
                ]
                
                if not cold_data.empty and not warm_data.empty:
                    results.append({
                        'language': lang,
                        'test_name': test_name,
                        'cold_time': cold_data.iloc[0]['execution_time_ms'],
                        'warm_time': warm_data['execution_time_ms'].mean()
                    })
        
        if not results:
            return
        
        comparison_df = pd.DataFrame(results)
        
        # Create grouped bar chart
        fig, ax = plt.subplots(figsize=(15, 8))
        
        x = np.arange(len(comparison_df))
        width = 0.35
        
        bars1 = ax.bar(x - width/2, comparison_df['cold_time'], width, 
                      label='Cold Start', alpha=0.8, color='lightcoral')
        bars2 = ax.bar(x + width/2, comparison_df['warm_time'], width,
                      label='Warmed Up', alpha=0.8, color='lightblue')
        
        ax.set_xlabel('Language - Test Combination')
        ax.set_ylabel('Execution Time (ms)')
        ax.set_title('Cold Start vs Warmed Up Performance Comparison', fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels([f"{row['language']}\n{row['test_name']}" for _, row in comparison_df.iterrows()],
                          rotation=45, ha='right')
        ax.legend()
        
        # Add improvement percentages
        for i, (_, row) in enumerate(comparison_df.iterrows()):
            improvement = ((row['cold_time'] - row['warm_time']) / row['cold_time']) * 100
            ax.text(i, max(row['cold_time'], row['warm_time']) + 50,
                   f'{improvement:+.1f}%', ha='center', va='bottom', fontweight='bold',
                   color='green' if improvement > 0 else 'red')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'cold_vs_warm_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()

def main():
    """Standalone chart generation"""
    try:
        with open('results/benchmark_results.json', 'r') as f:
            data = json.load(f)
        
        if data:
            df = pd.DataFrame(data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            generator = ChartGenerator(df)
            generator.generate_all_charts()
        else:
            print("No benchmark data found!")
            
    except FileNotFoundError:
        print("Results file not found. Run benchmarks first!")
    except Exception as e:
        print(f"Error generating charts: {e}")

if __name__ == "__main__":
    main()
