[{"timestamp": "2025-08-06T15:59:57Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 76, "memory_usage_mb": 17.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:57Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 72, "memory_usage_mb": 14.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:57Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 72, "memory_usage_mb": 23.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:57Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 73, "memory_usage_mb": -5.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:57Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 71, "memory_usage_mb": 5.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:57Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 71, "memory_usage_mb": 16.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:57Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 61, "memory_usage_mb": -7.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:58Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 71, "memory_usage_mb": -10.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:58Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 73, "memory_usage_mb": 4.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:58Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 71, "memory_usage_mb": 7.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:58Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 70, "memory_usage_mb": 20.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:58Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 72, "memory_usage_mb": 0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:58Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 61, "memory_usage_mb": 1.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:58Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 71, "memory_usage_mb": -17.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:58Z", "language": "Java", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 72, "memory_usage_mb": 1.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:58Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 9, "memory_usage_mb": 0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:58Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 7, "memory_usage_mb": 0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:58Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 9, "memory_usage_mb": -1.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:58Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 8, "memory_usage_mb": -4.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:58Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 8, "memory_usage_mb": 0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:58Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 9, "memory_usage_mb": -7.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:58Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 8, "memory_usage_mb": -6.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:59Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 9, "memory_usage_mb": -1.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:59Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 9, "memory_usage_mb": 0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:59Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 8, "memory_usage_mb": -7.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:59Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 9, "memory_usage_mb": 0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:59Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 8, "memory_usage_mb": 1.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:59Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 9, "memory_usage_mb": -4.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:59Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 9, "memory_usage_mb": 0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:59Z", "language": "C++", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 8, "memory_usage_mb": 1.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:59Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 57, "memory_usage_mb": 4.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:59Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 56, "memory_usage_mb": 19.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:59Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 57, "memory_usage_mb": 1.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:59Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 60, "memory_usage_mb": -2.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:59Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 56, "memory_usage_mb": 12.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T15:59:59Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 56, "memory_usage_mb": 6.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:59Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 58, "memory_usage_mb": 6.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T15:59:59Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 54, "memory_usage_mb": -3.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 58, "memory_usage_mb": -19.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 56, "memory_usage_mb": 2.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 55, "memory_usage_mb": -6.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 55, "memory_usage_mb": 21.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 56, "memory_usage_mb": 1.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 53, "memory_usage_mb": -16.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 57, "memory_usage_mb": -10.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 6, "memory_usage_mb": 0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:00Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 7, "memory_usage_mb": 0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:00Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 7, "memory_usage_mb": 1.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:00Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 7, "memory_usage_mb": 0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:00Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 6, "memory_usage_mb": -7.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:00Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 8, "memory_usage_mb": 0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 7, "memory_usage_mb": -6.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 7, "memory_usage_mb": 2.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 7, "memory_usage_mb": -1.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 7, "memory_usage_mb": 1.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 6, "memory_usage_mb": 0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 6, "memory_usage_mb": 0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:00Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 6, "memory_usage_mb": -7.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:01Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 7, "memory_usage_mb": 1.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:01Z", "language": "Rust", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 8, "memory_usage_mb": 0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:01Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 83, "memory_usage_mb": -3.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:01Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 91, "memory_usage_mb": -1.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:01Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 79, "memory_usage_mb": 17.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:01Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 79, "memory_usage_mb": -2.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:01Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 79, "memory_usage_mb": 1.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:01Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 78, "memory_usage_mb": -6.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:01Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 83, "memory_usage_mb": -3.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:01Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 80, "memory_usage_mb": 2.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:02Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 78, "memory_usage_mb": 2.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:02Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 83, "memory_usage_mb": 22.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:02Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 81, "memory_usage_mb": -28.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:02Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 77, "memory_usage_mb": -8.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:02Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 79, "memory_usage_mb": 0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:02Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 79, "memory_usage_mb": 2.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:02Z", "language": "Python", "test_category": "CPU", "test_name": "PrimeGeneration", "execution_time_ms": 79, "memory_usage_mb": 0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:02Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 60, "memory_usage_mb": -11.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:02Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 61, "memory_usage_mb": 4.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:02Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 62, "memory_usage_mb": 3.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:03Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 62, "memory_usage_mb": 19.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:03Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 61, "memory_usage_mb": 9.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:03Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 61, "memory_usage_mb": -12.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:03Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 63, "memory_usage_mb": 8.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:03Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 62, "memory_usage_mb": 9.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:03Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 63, "memory_usage_mb": 2.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:03Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 62, "memory_usage_mb": -8.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:03Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 60, "memory_usage_mb": 18.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:03Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 63, "memory_usage_mb": -9.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:03Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 61, "memory_usage_mb": -8.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:03Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 60, "memory_usage_mb": -8.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:03Z", "language": "Java", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 63, "memory_usage_mb": -7.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 5, "memory_usage_mb": -8.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 5, "memory_usage_mb": 0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 5, "memory_usage_mb": 0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 7, "memory_usage_mb": -8.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 6, "memory_usage_mb": 0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 6, "memory_usage_mb": -9.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 5, "memory_usage_mb": 0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 6, "memory_usage_mb": -3.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 5, "memory_usage_mb": 1.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 6, "memory_usage_mb": -4.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 6, "memory_usage_mb": 0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 6, "memory_usage_mb": 0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 6, "memory_usage_mb": 0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 6, "memory_usage_mb": 1.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:04Z", "language": "C++", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 5, "memory_usage_mb": -1.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:04Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 29, "memory_usage_mb": 2.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:04Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 27, "memory_usage_mb": 1.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:04Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 28, "memory_usage_mb": 4.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:04Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 30, "memory_usage_mb": 16.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:04Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 29, "memory_usage_mb": 7.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:04Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 31, "memory_usage_mb": 2.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:04Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 28, "memory_usage_mb": 4.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:04Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 27, "memory_usage_mb": -5.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:04Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 30, "memory_usage_mb": -10.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 28, "memory_usage_mb": 2.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 30, "memory_usage_mb": 7.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 28, "memory_usage_mb": 0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 29, "memory_usage_mb": 8.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 29, "memory_usage_mb": 1.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "JavaScript", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 38, "memory_usage_mb": 7.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 4, "memory_usage_mb": -8.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 4, "memory_usage_mb": 0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 5, "memory_usage_mb": 0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 4, "memory_usage_mb": 1.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 5, "memory_usage_mb": 0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 4, "memory_usage_mb": 0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 4, "memory_usage_mb": 1.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 5, "memory_usage_mb": 0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 5, "memory_usage_mb": 1.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 5, "memory_usage_mb": 0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 5, "memory_usage_mb": 0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 4, "memory_usage_mb": 0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 5, "memory_usage_mb": -2.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 4, "memory_usage_mb": 0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Rust", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 5, "memory_usage_mb": 0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 20, "memory_usage_mb": 0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 20, "memory_usage_mb": -1.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:05Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 20, "memory_usage_mb": 2.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:06Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 20, "memory_usage_mb": 1.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:06Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 20, "memory_usage_mb": 0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:06Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 20, "memory_usage_mb": -1.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:06Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 21, "memory_usage_mb": 4.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:06Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 21, "memory_usage_mb": -4.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:06Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 20, "memory_usage_mb": 5.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:06Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 19, "memory_usage_mb": 1.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:06Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 20, "memory_usage_mb": -4.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:06Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 21, "memory_usage_mb": -4.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:06Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 20, "memory_usage_mb": 6.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:06Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 20, "memory_usage_mb": 0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:06Z", "language": "Python", "test_category": "CPU", "test_name": "<PERSON><PERSON><PERSON><PERSON>", "execution_time_ms": 21, "memory_usage_mb": -11.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:06Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 229, "memory_usage_mb": -35.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:07Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 229, "memory_usage_mb": 4.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:07Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 235, "memory_usage_mb": 32.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:07Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 228, "memory_usage_mb": 12.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:07Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 225, "memory_usage_mb": -8.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:08Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 217, "memory_usage_mb": 5.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:08Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 223, "memory_usage_mb": -18.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:08Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 224, "memory_usage_mb": -4.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:08Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 224, "memory_usage_mb": -7.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:09Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 225, "memory_usage_mb": 23.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:09Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 222, "memory_usage_mb": 5.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:09Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 224, "memory_usage_mb": -16.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:09Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 230, "memory_usage_mb": 7.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:10Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 217, "memory_usage_mb": 11.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:10Z", "language": "Java", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 226, "memory_usage_mb": -19.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:10Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 113, "memory_usage_mb": 3.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:10Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 113, "memory_usage_mb": 2.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:10Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 113, "memory_usage_mb": 1.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:10Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 113, "memory_usage_mb": 9.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:11Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 112, "memory_usage_mb": -4.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:11Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 113, "memory_usage_mb": -7.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:11Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 114, "memory_usage_mb": 9.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:11Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 113, "memory_usage_mb": 3.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:11Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 114, "memory_usage_mb": 9.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:11Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 116, "memory_usage_mb": -14.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:11Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 114, "memory_usage_mb": -6.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:12Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 117, "memory_usage_mb": 3.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:12Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 119, "memory_usage_mb": -7.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:12Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 121, "memory_usage_mb": 0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:12Z", "language": "C++", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 116, "memory_usage_mb": -6.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:13Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 601, "memory_usage_mb": 28.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:13Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 585, "memory_usage_mb": 14.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:14Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 590, "memory_usage_mb": -14.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:14Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 589, "memory_usage_mb": -10.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:15Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 597, "memory_usage_mb": 2.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:16Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 581, "memory_usage_mb": -15.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:16Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 592, "memory_usage_mb": 3.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:17Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 605, "memory_usage_mb": 9.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:18Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 577, "memory_usage_mb": -5.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:18Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 589, "memory_usage_mb": 18.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:19Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 589, "memory_usage_mb": -25.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:19Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 593, "memory_usage_mb": -11.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:20Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 612, "memory_usage_mb": 18.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:21Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 601, "memory_usage_mb": -13.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:21Z", "language": "JavaScript", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 594, "memory_usage_mb": 24.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:22Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 378, "memory_usage_mb": 20.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:22Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 374, "memory_usage_mb": 1.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:22Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 374, "memory_usage_mb": -2.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:23Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 374, "memory_usage_mb": -9.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:23Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 373, "memory_usage_mb": 0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:24Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 375, "memory_usage_mb": -7.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:24Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 374, "memory_usage_mb": -2.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:24Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 375, "memory_usage_mb": -12.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:25Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 373, "memory_usage_mb": 2.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:25Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 376, "memory_usage_mb": 3.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:26Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 374, "memory_usage_mb": 4.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:26Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 377, "memory_usage_mb": -2.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:27Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 376, "memory_usage_mb": 5.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:27Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 381, "memory_usage_mb": 6.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:27Z", "language": "Rust", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 377, "memory_usage_mb": -80.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:00:40Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12706, "memory_usage_mb": 280.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:00:52Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12328, "memory_usage_mb": -174.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:01:05Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12308, "memory_usage_mb": -34.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:01:17Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12294, "memory_usage_mb": -38.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:01:29Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12214, "memory_usage_mb": 12.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:01:42Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12368, "memory_usage_mb": -77.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:01:54Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12162, "memory_usage_mb": -4.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:02:06Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12321, "memory_usage_mb": 3.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:02:19Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12366, "memory_usage_mb": 4.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:02:31Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12313, "memory_usage_mb": -2.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:02:43Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12322, "memory_usage_mb": 8.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:02:56Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12256, "memory_usage_mb": 10.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:08Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12323, "memory_usage_mb": 1.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:20Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12256, "memory_usage_mb": -21.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:33Z", "language": "Python", "test_category": "CPU", "test_name": "MatrixMultiplication", "execution_time_ms": 12426, "memory_usage_mb": 20.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:33Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 134, "memory_usage_mb": 2.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:33Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 138, "memory_usage_mb": 27.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:33Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 146, "memory_usage_mb": -18.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:33Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 146, "memory_usage_mb": 22.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:34Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 145, "memory_usage_mb": 0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:34Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 142, "memory_usage_mb": -1.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:34Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 138, "memory_usage_mb": -3.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:34Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 145, "memory_usage_mb": 18.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:34Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 147, "memory_usage_mb": 0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:34Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 144, "memory_usage_mb": 16.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:35Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 147, "memory_usage_mb": -24.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:35Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 147, "memory_usage_mb": 14.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:35Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 144, "memory_usage_mb": -11.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:35Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 146, "memory_usage_mb": -1.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:35Z", "language": "Java", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 139, "memory_usage_mb": -5.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:35Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 75, "memory_usage_mb": 0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:35Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 76, "memory_usage_mb": -9.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:36Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 76, "memory_usage_mb": 4.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:36Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 76, "memory_usage_mb": 1.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:36Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 77, "memory_usage_mb": -1.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:36Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 75, "memory_usage_mb": 0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:36Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 77, "memory_usage_mb": 0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:36Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 77, "memory_usage_mb": 3.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:36Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 77, "memory_usage_mb": 8.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:36Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 75, "memory_usage_mb": -3.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:36Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 80, "memory_usage_mb": -1.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:37Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 76, "memory_usage_mb": 8.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:37Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 84, "memory_usage_mb": 13.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:37Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 75, "memory_usage_mb": 0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:37Z", "language": "C++", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 76, "memory_usage_mb": 2.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:37Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 150, "memory_usage_mb": -15.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:37Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 150, "memory_usage_mb": 19.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:37Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 149, "memory_usage_mb": 12.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:38Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 149, "memory_usage_mb": -4.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:38Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 149, "memory_usage_mb": 9.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:38Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 154, "memory_usage_mb": 0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:38Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 152, "memory_usage_mb": -14.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:38Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 148, "memory_usage_mb": -5.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:38Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 152, "memory_usage_mb": -1.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:39Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 150, "memory_usage_mb": -4.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:39Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 151, "memory_usage_mb": 18.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:39Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 152, "memory_usage_mb": -3.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:39Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 151, "memory_usage_mb": -17.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:39Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 150, "memory_usage_mb": 3.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:40Z", "language": "JavaScript", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 149, "memory_usage_mb": 2.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:40Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 69, "memory_usage_mb": -4.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:40Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 69, "memory_usage_mb": -1.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:40Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 69, "memory_usage_mb": -4.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:40Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 70, "memory_usage_mb": 2.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:40Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 69, "memory_usage_mb": 3.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:40Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 69, "memory_usage_mb": -9.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:40Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 69, "memory_usage_mb": 2.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:40Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 70, "memory_usage_mb": -5.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:40Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 69, "memory_usage_mb": -1.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:41Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 71, "memory_usage_mb": 1.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:41Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 70, "memory_usage_mb": 2.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:41Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 70, "memory_usage_mb": -8.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:41Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 68, "memory_usage_mb": 5.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:41Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 71, "memory_usage_mb": -9.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:41Z", "language": "Rust", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 69, "memory_usage_mb": 4.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:03:44Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2955, "memory_usage_mb": -36.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:47Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2767, "memory_usage_mb": 31.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:50Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2750, "memory_usage_mb": -3.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:52Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2879, "memory_usage_mb": -12.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:55Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2740, "memory_usage_mb": -1.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:03:58Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2852, "memory_usage_mb": 4.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:01Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2955, "memory_usage_mb": 25.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:04Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2791, "memory_usage_mb": -40.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:07Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2838, "memory_usage_mb": -12.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:10Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2865, "memory_usage_mb": 36.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:13Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2882, "memory_usage_mb": 30.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:16Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2882, "memory_usage_mb": -14.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:18Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2814, "memory_usage_mb": 1.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:21Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2715, "memory_usage_mb": -6.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:24Z", "language": "Python", "test_category": "CPU", "test_name": "QuickSort", "execution_time_ms": 2891, "memory_usage_mb": -3.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:24Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 197, "memory_usage_mb": 5.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:24Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 195, "memory_usage_mb": -3.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:25Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 193, "memory_usage_mb": 11.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:25Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 206, "memory_usage_mb": -5.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:25Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 215, "memory_usage_mb": -4.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:25Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 195, "memory_usage_mb": -1.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:26Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 198, "memory_usage_mb": 16.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:26Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 199, "memory_usage_mb": 10.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:26Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 198, "memory_usage_mb": -23.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:26Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 199, "memory_usage_mb": -12.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:27Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 196, "memory_usage_mb": 12.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:27Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 213, "memory_usage_mb": 9.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:27Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 195, "memory_usage_mb": -4.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:27Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 194, "memory_usage_mb": -20.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:27Z", "language": "Java", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 195, "memory_usage_mb": 2.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:28Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 205, "memory_usage_mb": -1.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:28Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 218, "memory_usage_mb": 38.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:28Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 228, "memory_usage_mb": 66.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:28Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 223, "memory_usage_mb": 26.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:29Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 226, "memory_usage_mb": 37.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:29Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 234, "memory_usage_mb": 6.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:29Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 228, "memory_usage_mb": 7.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:29Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 209, "memory_usage_mb": -21.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:30Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 202, "memory_usage_mb": 0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:30Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 205, "memory_usage_mb": -5.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:30Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 203, "memory_usage_mb": -7.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:30Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 226, "memory_usage_mb": 82.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:31Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 221, "memory_usage_mb": 125.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:31Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 227, "memory_usage_mb": -134.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:31Z", "language": "C++", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 202, "memory_usage_mb": -2.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:32Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 415, "memory_usage_mb": 30.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:32Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 421, "memory_usage_mb": -4.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:32Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 415, "memory_usage_mb": 4.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:33Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 416, "memory_usage_mb": -31.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:33Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 415, "memory_usage_mb": -17.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:34Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 416, "memory_usage_mb": 2.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:34Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 417, "memory_usage_mb": -37.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:35Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 414, "memory_usage_mb": -15.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:35Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 415, "memory_usage_mb": 3.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:36Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 413, "memory_usage_mb": -3.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:36Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 415, "memory_usage_mb": 2.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:36Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 417, "memory_usage_mb": -1.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:37Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 425, "memory_usage_mb": -27.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:37Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 418, "memory_usage_mb": 9.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:38Z", "language": "JavaScript", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 418, "memory_usage_mb": 9.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:38Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 62, "memory_usage_mb": -11.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:38Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 62, "memory_usage_mb": 1.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:38Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 61, "memory_usage_mb": -7.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:38Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 82, "memory_usage_mb": -8.0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:38Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 63, "memory_usage_mb": -8.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:38Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 66, "memory_usage_mb": 0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:38Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 64, "memory_usage_mb": -1.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:39Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 65, "memory_usage_mb": 1.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:39Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 63, "memory_usage_mb": -3.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:39Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 67, "memory_usage_mb": -10.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:39Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 65, "memory_usage_mb": 0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:39Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 68, "memory_usage_mb": 4.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:39Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 63, "memory_usage_mb": 0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:39Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 66, "memory_usage_mb": 0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:39Z", "language": "Rust", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 64, "memory_usage_mb": 1.0, "iteration": 10, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:41Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1816, "memory_usage_mb": -8.0, "iteration": 1, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:43Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1468, "memory_usage_mb": 3.0, "iteration": 2, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:44Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1470, "memory_usage_mb": -1.0, "iteration": 3, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:46Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1482, "memory_usage_mb": 0, "iteration": 4, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:47Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1514, "memory_usage_mb": -17.0, "iteration": 5, "thread_count": 1, "is_warmup": true}, {"timestamp": "2025-08-06T16:04:49Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1539, "memory_usage_mb": -1.0, "iteration": 1, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:50Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1496, "memory_usage_mb": -8.0, "iteration": 2, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:52Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1601, "memory_usage_mb": -234.0, "iteration": 3, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:53Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1466, "memory_usage_mb": -5.0, "iteration": 4, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:55Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1484, "memory_usage_mb": 10.0, "iteration": 5, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:56Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1534, "memory_usage_mb": 6.0, "iteration": 6, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:04:58Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1599, "memory_usage_mb": 18.0, "iteration": 7, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:05:00Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1528, "memory_usage_mb": -29.0, "iteration": 8, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:05:01Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1491, "memory_usage_mb": -12.0, "iteration": 9, "thread_count": 1, "is_warmup": false}, {"timestamp": "2025-08-06T16:05:03Z", "language": "Python", "test_category": "CPU", "test_name": "PiCalculation", "execution_time_ms": 1587, "memory_usage_mb": -9.0, "iteration": 10, "thread_count": 1, "is_warmup": false}]