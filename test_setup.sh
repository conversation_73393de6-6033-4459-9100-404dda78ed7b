#!/bin/bash

# Quick test script to verify benchmark setup
# Tests a small subset of benchmarks to ensure everything works

set -e

echo "🧪 Testing Multi-Language Benchmark Setup"
echo "========================================="

# Create minimal results for testing
mkdir -p results analysis
echo "[]" > results/benchmark_results.json

# Test individual language implementations
echo "Testing individual language implementations..."

echo -n "  Java... "
if javac -d benchmarks/java/build benchmarks/java/CPUBenchmark.java 2>/dev/null; then
    if java -cp benchmarks/java/build CPUBenchmark prime 1000 >/dev/null 2>&1; then
        echo "✅"
    else
        echo "❌ (runtime error)"
    fi
else
    echo "❌ (compile error)"
fi

echo -n "  C++... "
if g++ -O3 -o benchmarks/cpp/build/test_cpu benchmarks/cpp/cpu_benchmark.cpp 2>/dev/null; then
    if benchmarks/cpp/build/test_cpu prime 1000 >/dev/null 2>&1; then
        echo "✅"
    else
        echo "❌ (runtime error)"
    fi
else
    echo "❌ (compile error)"
fi

echo -n "  JavaScript... "
if node benchmarks/javascript/cpu_benchmark.js prime 1000 >/dev/null 2>&1; then
    echo "✅"
else
    echo "❌"
fi

echo -n "  Python... "
if python3 benchmarks/python/cpu_benchmark.py prime 1000 >/dev/null 2>&1; then
    echo "✅"
else
    echo "❌"
fi

echo -n "  Rust... "
cd benchmarks/rust
if cargo build --release >/dev/null 2>&1; then
    if target/release/rust_benchmark cpu prime 1000 >/dev/null 2>&1; then
        echo "✅"
    else
        echo "❌ (runtime error)"
    fi
else
    echo "❌ (build error)"
fi
cd ../..

# Test Python analysis tools
echo ""
echo "Testing Python analysis tools..."

echo -n "  Installing Python dependencies... "
if pip3 install -r requirements.txt >/dev/null 2>&1; then
    echo "✅"
else
    echo "❌"
fi

echo -n "  Testing analysis script... "
if python3 -c "import pandas, matplotlib, seaborn, numpy; print('OK')" >/dev/null 2>&1; then
    echo "✅"
else
    echo "❌"
fi

echo ""
echo "🎉 Setup test completed!"
echo ""
echo "If all tests passed, you can run the full benchmark with:"
echo "  ./run_benchmark.sh"
