#!/bin/bash

# Multi-Language Benchmark Runner
# Runs benchmarks for Java, C++, JavaScript, Rust, and Python
# Generates comprehensive performance comparison data

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
RESULTS_DIR="results"
RESULTS_FILE="$RESULTS_DIR/benchmark_results.json"
WARMUP_ITERATIONS=5
BENCHMARK_ITERATIONS=10
THREAD_COUNTS=(1 2 4 8)

echo -e "${BLUE}=== Multi-Language Benchmark Suite ===${NC}"
echo "Languages: Java, C++, JavaScript (Node.js), Rust, Python3"
echo "Warmup iterations: $WARMUP_ITERATIONS"
echo "Benchmark iterations: $BENCHMARK_ITERATIONS"
echo "Thread counts: ${THREAD_COUNTS[*]}"
echo ""

# Create results directory
mkdir -p $RESULTS_DIR
echo "[]" > $RESULTS_FILE

# Function to log results
log_result() {
    local language=$1
    local test_category=$2
    local test_name=$3
    local execution_time=$4
    local memory_usage=$5
    local iteration=$6
    local thread_count=${7:-1}
    local is_warmup=${8:-false}
    
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Create JSON entry
    local json_entry=$(cat <<EOF
{
    "timestamp": "$timestamp",
    "language": "$language",
    "test_category": "$test_category",
    "test_name": "$test_name",
    "execution_time_ms": $execution_time,
    "memory_usage_mb": $memory_usage,
    "iteration": $iteration,
    "thread_count": $thread_count,
    "is_warmup": $is_warmup
}
EOF
)
    
    # Append to results file
    python3 -c "
import json
import sys

# Read existing results
with open('$RESULTS_FILE', 'r') as f:
    results = json.load(f)

# Add new result
new_result = $json_entry
results.append(new_result)

# Write back
with open('$RESULTS_FILE', 'w') as f:
    json.dump(results, f, indent=2)
"
}

# Function to run benchmark with timing and memory measurement
run_benchmark() {
    local language=$1
    local test_category=$2
    local test_name=$3
    local command=$4
    local thread_count=${5:-1}
    
    echo -e "${YELLOW}Running $language - $test_category - $test_name (threads: $thread_count)${NC}"
    
    # Warmup runs
    for i in $(seq 1 $WARMUP_ITERATIONS); do
        echo -n "  Warmup $i/$WARMUP_ITERATIONS... "
        
        # Measure execution time and memory
        local start_time=$(date +%s%3N)
        local memory_before=$(free -m | awk 'NR==2{printf "%.2f", $3}')
        
        # Run the command
        eval $command > /dev/null 2>&1
        
        local end_time=$(date +%s%3N)
        local memory_after=$(free -m | awk 'NR==2{printf "%.2f", $3}')
        
        local execution_time=$((end_time - start_time))
        local memory_usage=$(echo "$memory_after - $memory_before" | bc -l)
        
        log_result "$language" "$test_category" "$test_name" "$execution_time" "$memory_usage" "$i" "$thread_count" "True"
        echo "Done (${execution_time}ms)"
    done
    
    # Actual benchmark runs
    for i in $(seq 1 $BENCHMARK_ITERATIONS); do
        echo -n "  Benchmark $i/$BENCHMARK_ITERATIONS... "
        
        # Measure execution time and memory
        local start_time=$(date +%s%3N)
        local memory_before=$(free -m | awk 'NR==2{printf "%.2f", $3}')
        
        # Run the command
        eval $command > /dev/null 2>&1
        
        local end_time=$(date +%s%3N)
        local memory_after=$(free -m | awk 'NR==2{printf "%.2f", $3}')
        
        local execution_time=$((end_time - start_time))
        local memory_usage=$(echo "$memory_after - $memory_before" | bc -l)
        
        log_result "$language" "$test_category" "$test_name" "$execution_time" "$memory_usage" "$i" "$thread_count" "False"
        echo "Done (${execution_time}ms)"
    done
}

# Check dependencies
echo -e "${BLUE}Checking dependencies...${NC}"
check_dependency() {
    if command -v $1 &> /dev/null; then
        echo -e "  ${GREEN}✓${NC} $1 found"
        return 0
    else
        echo -e "  ${RED}✗${NC} $1 not found"
        return 1
    fi
}

MISSING_DEPS=0
check_dependency "java" || MISSING_DEPS=1
check_dependency "javac" || MISSING_DEPS=1
check_dependency "g++" || MISSING_DEPS=1
check_dependency "node" || MISSING_DEPS=1
check_dependency "cargo" || MISSING_DEPS=1
check_dependency "python3" || MISSING_DEPS=1
check_dependency "bc" || MISSING_DEPS=1

if [ $MISSING_DEPS -eq 1 ]; then
    echo -e "${RED}Missing dependencies. Please install them first.${NC}"
    exit 1
fi

echo ""

# Build phase
echo -e "${BLUE}Building benchmarks...${NC}"

# Build Java
echo -e "${YELLOW}Building Java benchmarks...${NC}"
mkdir -p benchmarks/java/build
javac -d benchmarks/java/build benchmarks/java/*.java

# Build C++
echo -e "${YELLOW}Building C++ benchmarks...${NC}"
mkdir -p benchmarks/cpp/build
g++ -O3 -std=c++17 -pthread -o benchmarks/cpp/build/cpu_benchmark benchmarks/cpp/cpu_benchmark.cpp

# Build Rust
echo -e "${YELLOW}Building Rust benchmarks...${NC}"
cd benchmarks/rust
cargo build --release
cd ../..

echo -e "${GREEN}Build completed!${NC}"
echo ""

# Start benchmarking
echo -e "${BLUE}Starting benchmarks...${NC}"
echo ""

# CPU-bound benchmarks
echo -e "${BLUE}=== CPU-BOUND BENCHMARKS ===${NC}"

# Prime number generation (1M limit)
echo -e "${YELLOW}Prime Number Generation (Sieve of Eratosthenes)${NC}"
run_benchmark "Java" "CPU" "PrimeGeneration" "java -cp benchmarks/java/build CPUBenchmark prime 1000000"
run_benchmark "C++" "CPU" "PrimeGeneration" "benchmarks/cpp/build/cpu_benchmark prime 1000000"
run_benchmark "JavaScript" "CPU" "PrimeGeneration" "node benchmarks/javascript/cpu_benchmark.js prime 1000000"
run_benchmark "Rust" "CPU" "PrimeGeneration" "benchmarks/rust/target/release/rust_benchmark cpu prime 1000000"
run_benchmark "Python" "CPU" "PrimeGeneration" "python3 benchmarks/python/cpu_benchmark.py prime 1000000"

# Fibonacci calculation
echo -e "${YELLOW}Fibonacci Calculation${NC}"
run_benchmark "Java" "CPU" "Fibonacci" "java -cp benchmarks/java/build CPUBenchmark fibonacci 45"
run_benchmark "C++" "CPU" "Fibonacci" "benchmarks/cpp/build/cpu_benchmark fibonacci 45"
run_benchmark "JavaScript" "CPU" "Fibonacci" "node benchmarks/javascript/cpu_benchmark.js fibonacci 45"
run_benchmark "Rust" "CPU" "Fibonacci" "benchmarks/rust/target/release/rust_benchmark cpu fibonacci 45"
run_benchmark "Python" "CPU" "Fibonacci" "python3 benchmarks/python/cpu_benchmark.py fibonacci 45"

# Matrix multiplication
echo -e "${YELLOW}Matrix Multiplication${NC}"
run_benchmark "Java" "CPU" "MatrixMultiplication" "java -cp benchmarks/java/build CPUBenchmark matrix 500"
run_benchmark "C++" "CPU" "MatrixMultiplication" "benchmarks/cpp/build/cpu_benchmark matrix 500"
run_benchmark "JavaScript" "CPU" "MatrixMultiplication" "node benchmarks/javascript/cpu_benchmark.js matrix 500"
run_benchmark "Rust" "CPU" "MatrixMultiplication" "benchmarks/rust/target/release/rust_benchmark cpu matrix 500"
run_benchmark "Python" "CPU" "MatrixMultiplication" "python3 benchmarks/python/cpu_benchmark.py matrix 500"

# Sorting benchmark
echo -e "${YELLOW}QuickSort Algorithm${NC}"
run_benchmark "Java" "CPU" "QuickSort" "java -cp benchmarks/java/build CPUBenchmark sort 1000000"
run_benchmark "C++" "CPU" "QuickSort" "benchmarks/cpp/build/cpu_benchmark sort 1000000"
run_benchmark "JavaScript" "CPU" "QuickSort" "node benchmarks/javascript/cpu_benchmark.js sort 1000000"
run_benchmark "Rust" "CPU" "QuickSort" "benchmarks/rust/target/release/rust_benchmark cpu sort 1000000"
run_benchmark "Python" "CPU" "QuickSort" "python3 benchmarks/python/cpu_benchmark.py sort 1000000"

# Pi calculation
echo -e "${YELLOW}Pi Calculation (Monte Carlo)${NC}"
run_benchmark "Java" "CPU" "PiCalculation" "java -cp benchmarks/java/build CPUBenchmark pi 10000000"
run_benchmark "C++" "CPU" "PiCalculation" "benchmarks/cpp/build/cpu_benchmark pi 10000000"
run_benchmark "JavaScript" "CPU" "PiCalculation" "node benchmarks/javascript/cpu_benchmark.js pi 10000000"
run_benchmark "Rust" "CPU" "PiCalculation" "benchmarks/rust/target/release/rust_benchmark cpu pi 10000000"
run_benchmark "Python" "CPU" "PiCalculation" "python3 benchmarks/python/cpu_benchmark.py pi 10000000"

echo ""
echo -e "${GREEN}Benchmarks completed!${NC}"
echo -e "${BLUE}Results saved to: $RESULTS_FILE${NC}"
echo ""

# Run analysis automatically
echo -e "${BLUE}Running analysis...${NC}"
python3 analysis/analyze_results.py --charts

echo ""
echo -e "${GREEN}Analysis completed! Check results/charts/ for visualizations.${NC}"
