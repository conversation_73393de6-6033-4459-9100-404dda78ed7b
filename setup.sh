#!/bin/bash

# Setup script for Multi-Language Benchmark Suite
# Installs dependencies and prepares environment

set -e

echo "🚀 Setting up Multi-Language Benchmark Suite"
echo "============================================="

# Check if running on Ubuntu/Debian
if command -v apt-get &> /dev/null; then
    echo "📦 Installing system dependencies (Ubuntu/Debian)..."
    sudo apt-get update
    sudo apt-get install -y \
        openjdk-17-jdk \
        g++ \
        nodejs \
        npm \
        python3 \
        python3-pip \
        curl \
        build-essential \
        bc
    
    # Install Rust
    if ! command -v cargo &> /dev/null; then
        echo "🦀 Installing Rust..."
        curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
        source ~/.cargo/env
    fi
    
elif command -v brew &> /dev/null; then
    echo "📦 Installing system dependencies (macOS)..."
    brew install openjdk@17 node python3 rust
    
elif command -v dnf &> /dev/null; then
    echo "📦 Installing system dependencies (Fedora)..."
    sudo dnf install -y \
        java-17-openjdk-devel \
        gcc-c++ \
        nodejs \
        npm \
        python3 \
        python3-pip \
        curl \
        bc
    
    # Install Rust
    if ! command -v cargo &> /dev/null; then
        echo "🦀 Installing Rust..."
        curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
        source ~/.cargo/env
    fi
else
    echo "⚠️  Unsupported package manager. Please install dependencies manually:"
    echo "   - Java 17+ (OpenJDK)"
    echo "   - C++ compiler (g++)"
    echo "   - Node.js 18+"
    echo "   - Python 3.9+"
    echo "   - Rust 1.70+"
    echo "   - bc (calculator)"
fi

# Install Python dependencies
echo "🐍 Installing Python dependencies..."
pip3 install -r requirements.txt

# Verify installations
echo ""
echo "✅ Verifying installations..."

check_command() {
    if command -v $1 &> /dev/null; then
        version=$($1 --version 2>&1 | head -n1)
        echo "  ✓ $1: $version"
        return 0
    else
        echo "  ✗ $1: Not found"
        return 1
    fi
}

MISSING=0
check_command java || MISSING=1
check_command javac || MISSING=1
check_command g++ || MISSING=1
check_command node || MISSING=1
check_command python3 || MISSING=1
check_command cargo || MISSING=1
check_command bc || MISSING=1

if [ $MISSING -eq 0 ]; then
    echo ""
    echo "🎉 Setup completed successfully!"
    echo "Run './run_benchmark.sh' to start benchmarking"
else
    echo ""
    echo "❌ Some dependencies are missing. Please install them manually."
    exit 1
fi
